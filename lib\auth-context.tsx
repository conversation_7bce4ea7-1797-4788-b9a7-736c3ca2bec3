"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { getToken, removeToken } from './api'

interface AuthContextType {
  isAuthenticated: boolean
  token: string | null
  logout: () => void
  setToken: (token: string) => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [token, setTokenState] = useState<string | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    // Check for existing token on mount
    const existingToken = getToken()
    if (existingToken) {
      setTokenState(existingToken)
      setIsAuthenticated(true)
    }
  }, [])

  const setToken = (newToken: string) => {
    setTokenState(newToken)
    setIsAuthenticated(true)
  }

  const logout = () => {
    removeToken()
    setTokenState(null)
    setIsAuthenticated(false)
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, token, logout, setToken }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 