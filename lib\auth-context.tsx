"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { getToken, removeToken } from './api'

interface AuthContextType {
  isAuthenticated: boolean
  token: string | null
  logout: () => void
  setToken: (token: string) => void
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [token, setTokenState] = useState<string | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check for existing token on mount
    const existingToken = getToken()
    if (existingToken) {
      setTokenState(existingToken)
      setIsAuthenticated(true)
    }
    setIsLoading(false) // Mark loading as complete
  }, [])

  const setToken = (newToken: string) => {
    setTokenState(newToken)
    setIsAuthenticated(true)
    setIsLoading(false)
  }

  const logout = () => {
    removeToken()
    setTokenState(null)
    setIsAuthenticated(false)
    setIsLoading(false)
  }

  // Add a global function for testing (only in development)
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    (window as any).setTestToken = (testToken: string) => {
      localStorage.setItem('access_token', testToken)
      setToken(testToken)
      console.log('Test token set successfully!')
    }
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, token, logout, setToken, isLoading }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 