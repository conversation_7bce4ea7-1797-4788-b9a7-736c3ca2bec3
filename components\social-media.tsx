"use client"

import { motion } from "framer-motion"
import { Facebook, Instagram, Linkedin, Twitter } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip"

export default function SocialMedia() {
  const socialLinks = [
    {
      name: "Discord",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="w-7 h-7"
        >
          <path d="M9 12a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
          <path d="M15 12a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
          <path d="M8.5 17c0 1 -1.356 3 -1.832 3c-1.429 0 -2.698 -1.667 -3.333 -3c-.635 -1.667 -.476 -5.833 1.428 -11.5c1.388 -1.015 3.025 -1.34 4.5 -1.5l.238 2.5" />
          <path d="M14.5 17c0 1 1.5 3 2 3c1.5 0 2.764 -1.667 3.5 -3c.736 -1.333 .476 -5.833 -1.5 -11.5c-1.457 -1.015 -3.248 -1.34 -4.5 -1.5l-.25 2.5" />
          <path d="M7 16.5c3.5 1 6.5 1 10 0" />
        </svg>
      ),
      url: "https://discord.gg/fundedwhales",
      followers: "12.4K",
      color: "bg-gradient-to-tr from-sky-400 via-blue-500 to-sky-500",
    },
    {
      name: "Facebook",
      icon: <Facebook className="w-7 h-7" />,
      url: "https://facebook.com/fundedwhales",
      followers: "8.2K",
      color: "bg-gradient-to-tr from-sky-500 to-sky-400",
    },
    {
      name: "Instagram",
      icon: <Instagram className="w-7 h-7" />,
      url: "https://instagram.com/fundedwhales",
      followers: "15.7K",
      color: "bg-gradient-to-tr from-pink-500 via-red-400 to-yellow-400",
    },
    {
      name: "Telegram",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="w-7 h-7"
        >
          <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
        </svg>
      ),
      url: "https://t.me/fundedwhales",
      followers: "6.9K",
      color: "bg-gradient-to-tr from-sky-400 to-blue-500",
    },
    {
      name: "LinkedIn",
      icon: <Linkedin className="w-7 h-7" />,
      url: "https://linkedin.com/company/fundedwhales",
      followers: "2.1K",
      color: "bg-gradient-to-tr from-sky-600 to-sky-400",
    },
    {
      name: "X (Twitter)",
      icon: <Twitter className="w-7 h-7" />,
      url: "https://twitter.com/fundedwhales",
      followers: "9.5K",
      color: "bg-gradient-to-tr from-sky-700 to-sky-400",
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
    hover: {
      scale: 1.2,
      y: -5,
      color: "#0ea5e9",
      transition: { duration: 0.3 },
    },
  }

  return (
    <section className="py-16 px-4 bg-gradient-to-b from-[#001a2c]/80 to-[#002a3c]/90 relative overflow-hidden">
      <div className="container mx-auto max-w-4xl flex flex-col items-center text-center z-10 relative">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-2 underwater-text">Connect with Funded Whales</h2>
        <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">Join our trading community and stay updated with the latest news, market insights, and exclusive offers. Follow us on your favorite platform:</p>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-6 w-full mb-8">
          {socialLinks.map((social, index) => (
            <motion.a
              key={index}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={`Follow us on ${social.name}`}
              whileHover={{ y: -8, scale: 1.08, boxShadow: "0 8px 32px 0 rgba(56,189,248,0.25)" }}
              className={`group flex flex-col items-center justify-center p-4 rounded-2xl shadow-xl bg-white/5 backdrop-blur-md border border-sky-500/10 transition-all duration-300 hover:scale-105 hover:border-sky-400/40 ${social.color}`}
                  >
              <div className="flex items-center justify-center w-14 h-14 rounded-full bg-white/10 mb-2 group-hover:bg-white/20 transition-all duration-300">
                    {social.icon}
              </div>
              <span className="text-white font-semibold text-base mt-1 mb-0.5 group-hover:text-sky-300 transition-colors">{social.name}</span>
              <span className="text-xs text-gray-200 group-hover:text-sky-200 transition-colors">{social.followers} followers</span>
            </motion.a>
          ))}
        </div>
        <div className="mt-2">
          <span className="inline-block bg-gradient-to-r from-sky-400 to-blue-500 text-white px-6 py-3 rounded-full font-semibold text-lg shadow-lg underwater-glow">Join 72,000+ traders in our global network</span>
        </div>
    </div>
      <div className="absolute -top-10 left-0 w-40 h-40 bg-sky-400/20 rounded-full blur-3xl opacity-40 z-0" />
      <div className="absolute -bottom-10 right-0 w-56 h-56 bg-blue-500/20 rounded-full blur-3xl opacity-30 z-0" />
    </section>
  )
}
