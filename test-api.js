// Simple test script to verify API endpoints
// Run with: node test-api.js

const API_BASE_URL = 'https://whales-backend-07e6b56a7fe0.herokuapp.com';

async function testOrderIds() {
  console.log('Testing Order IDs endpoint...');
  try {
    const response = await fetch(`${API_BASE_URL}/order/order_ids`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer YOUR_TOKEN_HERE', // Replace with actual token
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('Order IDs response:', data);
    return data;
  } catch (error) {
    console.error('Error fetching order IDs:', error);
    return null;
  }
}

async function testOrderDetails(orderId) {
  console.log(`Testing Order Details endpoint for ID: ${orderId}...`);
  try {
    // Extract numeric part from order ID (remove "FxE" prefix)
    const numericOrderId = orderId.replace(/^FxE/i, '');
    console.log(`Using numeric ID: ${numericOrderId}`);
    
    const response = await fetch(`${API_BASE_URL}/order/account_detail/${numericOrderId}`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer YOUR_TOKEN_HERE', // Replace with actual token
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('Order Details response:', data);
    return data;
  } catch (error) {
    console.error('Error fetching order details:', error);
    return null;
  }
}

async function runTests() {
  console.log('=== API Integration Test ===\n');
  
  // Test 1: Get order IDs
  const orders = await testOrderIds();
  
  if (orders && orders.length > 0) {
    console.log(`\nFound ${orders.length} orders`);
    
    // Test 2: Get details for first order
    const firstOrder = orders[0];
    console.log(`\nTesting with first order: ${firstOrder.order_id}`);
    await testOrderDetails(firstOrder.order_id);
  } else {
    console.log('\nNo orders found, testing with sample ID...');
    await testOrderDetails('FxE43940307');
  }
  
  console.log('\n=== Test Complete ===');
}

// Run tests
runTests();
