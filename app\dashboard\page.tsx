"use client"

import { useState, useEffect, useMemo } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { getOrders, getOrderDetails } from "@/lib/api"
import {
  BarChart3,
  Network,
  Wallet,
  TrendingUp,
  Shield,
  Copy,
  Eye,
  EyeOff,
  ChevronDown,
  Mail,
  Bell,
  Settings,
  LogOut,
  CheckCircle,
  Boxes,
  List,
  Search,
  BookOpen,
  AlertTriangle,
  Clock,
  Target,
  Zap,
  Award,
  Users,
  Activity,
  Globe,
  Lock,
  RefreshCw,
  ArrowDown,
  User
} from "lucide-react"

export default function DashboardPage() {
  console.log('DashboardPage component rendering...')

  // Add error boundary for client-side errors
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    console.log('Setting up error handler...')
    const handleError = (error: ErrorEvent) => {
      console.error('Client-side error caught:', error)
      setHasError(true)
    }

    window.addEventListener('error', handleError)
    return () => window.removeEventListener('error', handleError)
  }, [])

  if (hasError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] p-6">
        <div className="w-full max-w-7xl mx-auto flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-white text-2xl mb-4">Something went wrong</h1>
            <p className="text-gray-400 mb-4">Please refresh the page to try again.</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-sky-500 hover:bg-sky-600 text-white px-4 py-2 rounded"
            >
              Refresh Page
            </button>
          </div>
        </div>
      </div>
    )
  }
  const [showPassword, setShowPassword] = useState(false)
  const [showLoginId, setShowLoginId] = useState(false)
  const [showServer, setShowServer] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null)
  const [showOrderSelector, setShowOrderSelector] = useState(false)
  const [lastUpdate, setLastUpdate] = useState(new Date())
  const [isOnline, setIsOnline] = useState(true)
  const [apiError, setApiError] = useState<string | null>(null)
  const [orders, setOrders] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentOrderDetails, setCurrentOrderDetails] = useState<any>(null)
  const [isLoadingDetails, setIsLoadingDetails] = useState(false)

  // Fetch orders from API
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setIsLoading(true)
        setApiError(null) // Clear previous errors

        // Always try to fetch from API first, fallback to mock data if it fails
        try {
          const ordersData = await getOrders()
          console.log('Fetched orders data:', ordersData)

          // Ensure ordersData is an array
          const validOrders = Array.isArray(ordersData) ? ordersData : []
          setOrders(validOrders)
          setIsOnline(true)

          // Set the first order as selected if no order is currently selected
          if (!selectedOrderId && validOrders.length > 0 && validOrders[0]?.id) {
            setSelectedOrderId(validOrders[0].id)
          }
          return // Success, exit early
        } catch (apiError) {
          console.warn('API call failed, using mock data:', apiError)
          setIsOnline(false)
          // Continue to mock data below
        }

        // Fallback to mock data
        const defaultOrders = [
          {
            id: "FxE43940307",
            name: "Professional Account",
            platform: "MetaTrader 5",
            loginId: "demo_user",
            password: "demo123456",
            server: "FundedWhales-Live1",
            balance: "$10,000",
            profit: "+$0",
            status: "Active",
            riskLevel: "Medium",
            drawdown: "0%",
            openTrades: 0,
            totalTrades: 0,
            username: "demo_user"
          },
          {
            id: "FxE82136447",
            name: "Standard Account",
            platform: "MetaTrader 4",
            loginId: "demo_user",
            password: "demo123456",
            server: "FundedWhales-Live2",
            balance: "$1,000",
            profit: "+$0",
            status: "Pending",
            riskLevel: "Low",
            drawdown: "0%",
            openTrades: 0,
            totalTrades: 0,
            username: "demo_user"
          }
        ]
        setOrders(defaultOrders)
        if (!selectedOrderId) {
          setSelectedOrderId(defaultOrders[0].id)
        }
      } finally {
        setIsLoading(false)
      }
    }

    fetchOrders()
  }, [])

  // Real-time data simulation
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdate(new Date())
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  // Get current order credentials - use detailed order info if available, otherwise fall back to basic order info
  const currentOrder = useMemo(() => {
    if (currentOrderDetails) return currentOrderDetails
    if (selectedOrderId && orders.length > 0) {
      return orders.find(order => order?.id === selectedOrderId) || orders[0]
    }
    return orders.length > 0 ? orders[0] : null
  }, [currentOrderDetails, selectedOrderId, orders])

  const userEmails = [
    { email: "<EMAIL>", status: "Active", type: "Primary" },
    { email: "<EMAIL>", status: "Verified", type: "Trading" },
    { email: "<EMAIL>", status: "Pending", type: "Alerts" }
  ]

  // Show loading state while fetching orders
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] p-6">
        <div className="w-full max-w-7xl mx-auto flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-400 mx-auto mb-4"></div>
            <p className="text-white text-lg">Loading dashboard...</p>
          </div>
        </div>
      </div>
    )
  }

  // If no orders are available, show empty state
  if (!currentOrder && orders.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] p-6">
        <div className="w-full max-w-7xl mx-auto flex items-center justify-center min-h-screen">
          <div className="text-center">
            <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <p className="text-white text-lg mb-2">No orders found</p>
            <p className="text-gray-400">You don't have any trading orders yet.</p>
          </div>
        </div>
      </div>
    )
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  }

  const dropdownVariants = {
    hidden: { opacity: 0, y: -10, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { duration: 0.2, ease: "easeOut" }
    },
    exit: {
      opacity: 0,
      y: -10,
      scale: 0.95,
      transition: { duration: 0.2, ease: "easeIn" }
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const fetchOrderDetails = async (orderId: string) => {
    try {
      setIsLoadingDetails(true)

      // Fetch detailed order information
      const orderDetails = await getOrderDetails(orderId)
      console.log('Fetched order details for', orderId, ':', orderDetails)
      setCurrentOrderDetails(orderDetails)
    } catch (error) {
      console.error('Failed to fetch order details:', error)
      setApiError(error instanceof Error ? error.message : 'Failed to fetch order details')
      // Keep the current order details if fetch fails
    } finally {
      setIsLoadingDetails(false)
    }
  }

  const handleOrderSelect = (orderId: string) => {
    setSelectedOrderId(orderId)
    setShowOrderSelector(false)
  }

  // Fetch order details when selectedOrderId changes
  useEffect(() => {
    if (selectedOrderId && !isLoading) {
      fetchOrderDetails(selectedOrderId)
    }
  }, [selectedOrderId, isLoading])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] p-6 w-full max-w-full overflow-x-hidden">
      {/* Top Navigation Bar */}
      <motion.div
        className="w-full max-w-full mx-auto mb-6 px-2"
        variants={fadeInUp}
        initial="hidden"
        animate="visible"
      >
        <div className="flex items-center justify-between bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl rounded-2xl p-6 border border-sky-900/60 shadow-2xl">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-sky-400 to-sky-500 flex items-center justify-center text-white font-bold text-lg shadow-lg">
              S
            </div>
            <div>
              <div className="flex items-center gap-4">
                <h1 className="text-white text-2xl font-bold">Professional Trading Dashboard</h1>
                {/* Order ID Selector Dropdown */}
                <div className="relative">
                  <Button
                    className="bg-sky-500/20 hover:bg-sky-500/30 text-white border border-sky-500/40 backdrop-blur-sm"
                    onClick={() => setShowOrderSelector(!showOrderSelector)}
                    disabled={isLoading}
                  >
                    {isLoadingDetails ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <List className="h-4 w-4 mr-2" />
                    )}
                    {selectedOrderId || 'Select Order ID'}
                    <ChevronDown className={`h-4 w-4 ml-2 transition-transform duration-300 ${showOrderSelector ? 'rotate-180' : ''}`} />
                  </Button>
                  <AnimatePresence>
                    {showOrderSelector && (
                      <motion.div
                        className="absolute left-0 top-full mt-2 w-64 rounded-xl bg-gradient-to-br from-[#001a2c] via-[#002a3c] to-[#003a4c] backdrop-blur-lg border border-sky-900/60 shadow-2xl z-[9999]"
                        variants={dropdownVariants}
                        initial="hidden"
                        animate="visible"
                        exit="hidden"
                      >
                        <div className="p-2">
                          {isLoading ? (
                            <div className="flex items-center justify-center py-4">
                              <RefreshCw className="h-5 w-5 animate-spin text-sky-400" />
                              <span className="ml-2 text-gray-300">Loading orders...</span>
                            </div>
                          ) : orders.length === 0 ? (
                            <div className="text-center py-4 text-gray-400">
                              No orders found
                            </div>
                          ) : (
                            orders.map((order) => (
                              <button
                                key={order.id}
                                className={`w-full text-left px-3 py-2 rounded-lg transition-colors duration-200 ${
                                  selectedOrderId === order.id
                                    ? 'bg-sky-500/20 text-sky-300'
                                    : 'text-gray-300 hover:bg-sky-500/10 hover:text-white'
                                }`}
                                onClick={() => {
                                  setSelectedOrderId(order.id)
                                  setShowOrderSelector(false)
                                }}
                                disabled={isLoadingDetails}
                              >
                              <div className="flex items-center justify-between">
                                <span className="font-medium">{order.id}</span>
                                <Badge className={`text-xs ${
                                  order.status === 'Active' ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
                                }`}>
                                  {order.status}
                                </Badge>
                              </div>
                              <div className="text-xs text-gray-400 mt-1">{order.name}</div>
                            </button>
                          ))
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
              <div className="flex items-center space-x-3 mt-1">
                <p className="text-sky-300 text-sm">Enterprise Account</p>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-400' : 'bg-red-400'}`}></div>
                  <span className="text-xs text-gray-300">{isOnline ? 'Online' : 'Offline'}</span>
                </div>
                <span className="text-xs text-gray-400">Last updated: {formatTime(lastUpdate)}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button className="bg-sky-500/20 hover:bg-sky-500/30 text-white border border-sky-500/40 backdrop-blur-sm">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
            </Button>
            <Button className="bg-sky-500/20 hover:bg-sky-500/30 text-white border border-sky-500/40 backdrop-blur-sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <div className="relative">
              <Button
                className="bg-sky-500/20 hover:bg-sky-500/30 text-white border border-sky-500/40 backdrop-blur-sm"
                onClick={() => setShowDropdown(!showDropdown)}
              >
                <Mail className="h-4 w-4 mr-2" />
                Accounts
                <ChevronDown className={`h-4 w-4 ml-2 transition-transform duration-300 ${showDropdown ? 'rotate-180' : ''}`} />
              </Button>
              <AnimatePresence>
                {showDropdown && (
                  <motion.div
                    className="absolute right-0 top-full mt-2 w-80 rounded-xl bg-gradient-to-br from-[#001a2c] via-[#002a3c] to-[#003a4c] backdrop-blur-lg border border-sky-900/60 shadow-2xl z-[9999]"
                    variants={dropdownVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                  >
                    <div className="p-4 border-b border-sky-900/60">
                      <h3 className="text-white font-medium">Connected Accounts</h3>
                      <p className="text-sm text-gray-300">Manage your trading emails</p>
                    </div>
                    <div className="p-2">
                      {userEmails.map((item, index) => (
                        <div
                          key={index}
                          className="p-3 hover:bg-sky-500/20 rounded-lg transition-colors duration-200 cursor-pointer group"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                                item.type === 'Primary' ? 'bg-sky-400/20 text-sky-400' :
                                item.type === 'Trading' ? 'bg-sky-500/20 text-sky-500' :
                                'bg-yellow-400/20 text-yellow-400'
                              }`}>
                                <Mail className="h-4 w-4" />
                              </div>
                              <div>
                                <p className="text-white text-sm font-medium">{item.email}</p>
                                <div className="flex items-center space-x-2">
                                  <span className="text-xs text-gray-400">{item.type}</span>
                                  <span className={`text-xs px-2 py-0.5 rounded-full ${
                                    item.status === 'Active' ? 'bg-sky-400/20 text-sky-400' :
                                    item.status === 'Verified' ? 'bg-sky-500/20 text-sky-500' :
                                    'bg-yellow-400/20 text-yellow-400'
                                  }`}>
                                    {item.status}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <CheckCircle className={`h-4 w-4 ${
                              item.type === 'Primary' ? 'text-sky-400' : 'text-gray-600'
                            }`} />
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="p-2 border-t border-sky-900/60">
                      <Button className="w-full bg-red-500/20 hover:bg-red-500/30 text-red-300 border-0">
                        <LogOut className="h-4 w-4 mr-2" />
                        Sign Out
                      </Button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content Grid */}
      <div className="w-full max-w-full mx-auto grid grid-cols-1 lg:grid-cols-3 gap-6 px-2">
        
        {/* Left Column - Account Details */}
        <motion.div 
          className="lg:col-span-2 space-y-6"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.1 }}
        >
          {/* Account Details Card */}
          <Card className="bg-gradient-to-br from-[#0a2236] via-[#0e2d47] to-[#071624] border border-sky-900/60 rounded-2xl shadow-2xl p-0 overflow-hidden">
            <div className="px-8 pt-8 pb-4">
              <div className="flex items-center gap-3 mb-2">
                <h2 className="text-2xl font-bold text-white flex items-center gap-2">
                    <BarChart3 className="h-6 w-6 text-sky-400" />
                    Account Details
                  <span className="ml-2 h-2 w-2 rounded-full bg-green-400 inline-block"></span>
                </h2>
              </div>
              <p className="text-gray-400 mb-6">Manage your trading account credentials</p>
              <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-8">
                {/* Platform */}
                <div className="flex flex-col items-center bg-gradient-to-br from-[#0e2d47]/80 to-[#0a2236]/80 rounded-xl p-4 border border-sky-900/40">
                  <Globe className="h-6 w-6 text-sky-400 mb-2" />
                  <span className="text-xs text-gray-400">Platform</span>
                  <span className="text-lg font-bold text-white mt-1">{currentOrder?.platform || 'Not provided'}</span>
                </div>
                {/* Server */}
                <div className="flex flex-col items-center bg-gradient-to-br from-[#0e2d47]/80 to-[#0a2236]/80 rounded-xl p-4 border border-sky-900/40">
                  <Boxes className="h-6 w-6 text-purple-400 mb-2" />
                  <span className="text-xs text-gray-400">Server</span>
                  <span className="text-lg font-bold text-white mt-1">{currentOrder?.server || 'Not provided'}</span>
                            </div>
                {/* Account Size */}
                <div className="flex flex-col items-center bg-gradient-to-br from-[#1a2a3c]/80 to-[#0a2236]/80 rounded-xl p-4 border border-sky-900/40">
                  <Wallet className="h-6 w-6 text-yellow-400 mb-2" />
                  <span className="text-xs text-gray-400">Account Size</span>
                  <span className="text-lg font-bold text-white mt-1">{currentOrder?.account_size || 'Not provided'}</span>
                                      </div>
                {/* Profit Target */}
                <div className="flex flex-col items-center bg-gradient-to-br from-[#1a2a3c]/80 to-[#0a2236]/80 rounded-xl p-4 border border-sky-900/40">
                  <Shield className="h-6 w-6 text-green-400 mb-2" />
                  <span className="text-xs text-gray-400">Profit Target</span>
                  <span className="text-lg font-bold text-white mt-1">{currentOrder?.profit_target || 'Not provided'}</span>
                                        </div>
                {/* Account Type */}
                <div className="flex flex-col items-center bg-gradient-to-br from-[#2a1a3c]/80 to-[#0a2236]/80 rounded-xl p-4 border border-sky-900/40">
                  <Award className="h-6 w-6 text-pink-400 mb-2" />
                  <span className="text-xs text-gray-400">Account Type</span>
                  <span className="text-lg font-bold text-white mt-1">{currentOrder?.challenge_type || 'Not provided'}</span>
                                      </div>
                {/* Status */}
                <div className="flex flex-col items-center bg-gradient-to-br from-[#2a3c1a]/80 to-[#0a2236]/80 rounded-xl p-4 border border-sky-900/40">
                  <CheckCircle className={`h-6 w-6 mb-2 ${currentOrder?.status === 'Active' ? 'text-green-400' : 'text-yellow-400'}`} />
                  <span className="text-xs text-gray-400">Status</span>
                  <span className={`text-lg font-bold mt-1 ${currentOrder?.status === 'Active' ? 'text-green-400' : 'text-yellow-400'}`}>{currentOrder?.status || 'Not provided'}</span>
                </div>
              </div>
              {/* Account Credentials - now full width */}
              <div className="w-full bg-gradient-to-br from-[#1a2a3c]/80 to-[#0a2236]/80 rounded-2xl p-6 border border-sky-900/40">
                <h3 className="text-lg font-semibold text-sky-400 mb-4 flex items-center gap-2">
                  <Network className="h-5 w-5 text-sky-400" />
                  Account Credentials
                </h3>
                <div className="space-y-4">
                {/* Login ID */}
                  <div className="flex items-center justify-between bg-[#0e2d47]/60 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <User className="h-5 w-5 text-sky-400" />
                      <span className="text-white font-medium">Login ID</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-gray-400">{currentOrder?.loginId || currentOrder?.username || currentOrder?.platform_login || 'Not provided'}</span>
                      <Button variant="ghost" size="icon" className="text-sky-400 hover:text-white" onClick={() => copyToClipboard(currentOrder?.loginId || currentOrder?.username || currentOrder?.platform_login || '')} disabled={!currentOrder?.loginId && !currentOrder?.username && !currentOrder?.platform_login}>
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                {/* Password */}
                  <div className="flex items-center justify-between bg-[#0e2d47]/60 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <Lock className="h-5 w-5 text-sky-400" />
                      <span className="text-white font-medium">Password</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-gray-400">{currentOrder?.platform_password ? '************' : 'Not provided'}</span>
                      <Button variant="ghost" size="icon" className="text-sky-400 hover:text-white" onClick={() => copyToClipboard(currentOrder?.platform_password || '')} disabled={!currentOrder?.platform_password}>
                        <Copy className="h-4 w-4" />
                        </Button>
                    </div>
                  </div>
                {/* Server */}
                  <div className="flex items-center justify-between bg-[#0e2d47]/60 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <Boxes className="h-5 w-5 text-sky-400" />
                      <span className="text-white font-medium">Server</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-gray-400">{currentOrder?.server || 'Not provided'}</span>
                      <Button variant="ghost" size="icon" className="text-sky-400 hover:text-white" onClick={() => copyToClipboard(currentOrder?.server || '')} disabled={!currentOrder?.server}>
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              </div>
          </Card>

        </motion.div>

        {/* Right Column - Quick Actions & Info */}
        <motion.div 
          className="space-y-6"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.2 }}
        >
          {/* Quick Actions */}
          <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60 rounded-2xl overflow-hidden shadow-2xl">
            <CardHeader className="bg-gradient-to-r from-sky-500/20 to-sky-400/20 border-b border-sky-900/60 p-4">
              <CardTitle className="text-white flex items-center gap-2">
                <Zap className="h-5 w-5 text-sky-400" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 space-y-3">
              <Button className="w-full bg-gradient-to-r from-sky-500 to-sky-400 hover:from-sky-600 hover:to-sky-500 text-white">
                <BarChart3 className="h-4 w-4 mr-2" />
                Start New Challenge
              </Button>
              <Button className="w-full bg-sky-500/10 hover:bg-sky-500/20 text-white border border-sky-500/20">
                <Wallet className="h-4 w-4 mr-2" />
                Make Deposit
              </Button>
              <Button className="w-full bg-sky-500/10 hover:bg-sky-500/20 text-white border border-sky-500/20">
                <TrendingUp className="h-4 w-4 mr-2" />
                Withdraw Funds
              </Button>
              <Button className="w-full bg-sky-500/10 hover:bg-sky-500/20 text-white border border-sky-500/20">
                <Shield className="h-4 w-4 mr-2" />
                KYC Verification
              </Button>
            </CardContent>
          </Card>

          {/* Trading Rules */}
          <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60 rounded-2xl overflow-hidden shadow-2xl">
            <CardHeader className="bg-gradient-to-r from-orange-500/20 to-red-500/20 border-b border-sky-900/60 p-4">
              <CardTitle className="text-white flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-orange-400" />
                Trading Rules
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 space-y-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-red-400 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="text-white text-sm font-medium">Maximum Daily Loss</p>
                    <p className="text-gray-400 text-xs">5% of account balance</p>
              </div>
              </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="text-white text-sm font-medium">Maximum Position Size</p>
                    <p className="text-gray-400 text-xs">2% of account balance</p>
              </div>
              </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                <div className="flex-1">
                    <p className="text-white text-sm font-medium">Minimum Trade Duration</p>
                    <p className="text-gray-400 text-xs">5 minutes</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-sky-400 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="text-white text-sm font-medium">News Trading</p>
                    <p className="text-gray-400 text-xs">Not allowed during high impact news</p>
                </div>
              </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mt-2"></div>
                <div className="flex-1">
                    <p className="text-white text-sm font-medium">Weekend Trading</p>
                    <p className="text-gray-400 text-xs">Friday 22:00 - Sunday 22:00 UTC</p>
                  </div>
                </div>
              </div>
              
              <div className="mt-4 pt-4 border-t border-sky-900/60">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  <span className="text-yellow-400 text-sm font-medium">Important Reminders</span>
                </div>
                <div className="space-y-2">
                  <p className="text-gray-300 text-xs">• Always use stop loss and take profit</p>
                  <p className="text-gray-300 text-xs">• Avoid over-leveraging your positions</p>
                  <p className="text-gray-300 text-xs">• Monitor your drawdown closely</p>
                </div>
              </div>
            </CardContent>
          </Card>


        </motion.div>
      </div>
    </div>
  )
}
