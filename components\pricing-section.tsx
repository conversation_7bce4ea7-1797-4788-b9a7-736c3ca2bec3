"use client"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Check, HelpCircle, Star, Crown } from "lucide-react"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useState } from "react"
import { FaBitcoin } from "react-icons/fa"
import { SiEthereum, SiBinance } from "react-icons/si"

export default function PricingSection() {
  const steps = ["1STEP", "2STEP", "HFT PRO", "INSTANT"] as const;
  const amounts = ["1K", "3K", "5K", "10K", "25K", "50K", "100K", "200K", "500K"] as const;
  const amountMap: Record<string, string> = {
    "1K": "$1K",
    "3K": "$3K",
    "5K": "$5K",
    "10K": "$10K",
    "25K": "$25K",
    "50K": "$50K",
    "100K": "$100K",
    "200K": "$200K",
    "500K": "$500K",
  };
  const [selectedStep, setSelectedStep] = useState<string>("1STEP");
  const [selectedAmount, setSelectedAmount] = useState<string>("100K");

  // Pricing data from spreadsheet (discounted and real)
  const priceTable: Record<string, { real: number[]; discount: number[] }> = {
    "INSTANT": {
      real:   [56, 131, 231, 419, 883, 1930, 3430, 5650, 11230],
      discount: [45, 105, 185, 335, 706, 772, 1372, 2260, 4492],
    },
    "HFT PRO": {
      real:   [18, 35, 47, 80, 175, 290, 510, 819, 1490],
      discount: [14, 28, 38, 64, 140, 116, 204, 328, 596],
    },
    "1STEP": {
      real:   [10, 16, 28, 48, 92, 167, 248, 425, 875],
      discount: [8, 13, 22, 38, 74, 67, 99, 170, 350],
    },
    "2STEP": {
      real:   [8, 14, 25, 33, 69, 125, 188, 313, 985],
      discount: [6, 11, 20, 26, 55, 50, 75, 125, 394],
    },
  };

  // Find index for selected amount
  const amountIdx = amounts.indexOf(selectedAmount as any);
  // Get prices for selected plan and amount
  const realPrice = priceTable[selectedStep]?.real[amountIdx] || 0;
  const discountPrice = priceTable[selectedStep]?.discount[amountIdx] || 0;

  // Plan details (static for demo, can be dynamic per plan/amount)
  const planDetails = {
    target: selectedStep === "INSTANT" ? "None" : selectedStep === "HFT PRO" ? "2%" : selectedStep === "2STEP" ? "6%" : "3%",
    dailyDrawdown: selectedStep === "HFT PRO" ? "2%" : selectedStep === "2STEP" ? "4%" : "3%",
    maxDrawdown: selectedStep === "INSTANT" ? "10%" : selectedStep === "2STEP" ? "8%" : selectedStep === "HFT PRO" ? "4%" : "5%",
    minTradingDays: selectedStep === "INSTANT" ? "0 Days" : selectedStep === "HFT PRO" ? "3 Days" : selectedStep === "2STEP" ? "10 Days" : "5 Days",
    rewardSchedule: selectedStep === "INSTANT" ? "Instant" : selectedStep === "HFT PRO" ? "Weekly" : selectedStep === "2STEP" ? "Monthly" : "Bi-weekly - add on On-demand",
    leverage: selectedStep === "INSTANT" ? "Up to 1:30" : selectedStep === "HFT PRO" ? "Up to 1:200" : selectedStep === "2STEP" ? "Up to 1:50" : "Up to 1:100",
    profitSplit: selectedStep === "INSTANT" ? "70% standard" : selectedStep === "HFT PRO" ? "90% standard - add on 100%" : selectedStep === "2STEP" ? "85% standard - add on 100%" : "80% standard - add on 100%",
    label: selectedStep === "INSTANT" ? "INSTANT ACCOUNT" : selectedStep === "HFT PRO" ? "HFT PRO ACCOUNT" : selectedStep === "2STEP" ? "PRO ACCOUNT" : "GOAT ACCOUNT",
  };

  const paymentMethods = [
    { icon: <span className="text-4xl font-bold text-sky-400">$</span>, label: "Dollar" },
    { icon: <FaBitcoin className="text-4xl text-yellow-400" />, label: "Bitcoin" },
    { icon: <SiEthereum className="text-4xl text-blue-400" />, label: "Ethereum" },
    { icon: <SiBinance className="text-4xl text-yellow-500" />, label: "BNB" },
  ];
  const discount = "30% OFF + 100% REFUND";
  const cta = "START CHALLENGE";

  return (
    <TooltipProvider>
      <main className="min-h-screen py-24 bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] text-white flex flex-col items-center justify-center">
        {/* Step and Amount Selectors */}
        <div className="flex flex-wrap justify-center gap-4 mb-8 text-lg">
          {steps.map((step) => (
            <button
              key={step}
              className={`px-6 py-3 rounded-xl font-bold border-2 transition-all text-xl shadow-md focus:outline-none focus:ring-2 focus:ring-sky-400 focus:ring-offset-2 focus:ring-offset-[#001a2c] duration-200 transform ${selectedStep === step ? "bg-sky-500 text-white border-sky-500 shadow-lg scale-105" : "bg-[#001a2c] border-[#003a4c] text-white hover:bg-sky-400 hover:text-white hover:scale-105"}`}
              onClick={() => setSelectedStep(step)}
            >
              {step}
            </button>
          ))}
        </div>
        <div className="flex flex-wrap justify-center gap-4 mb-10 text-lg">
          {amounts.map((amt) => (
            <button
              key={amt}
              className={`px-6 py-3 rounded-xl font-bold border-2 transition-all text-xl shadow-md focus:outline-none focus:ring-2 focus:ring-sky-400 focus:ring-offset-2 focus:ring-offset-[#001a2c] duration-200 transform ${selectedAmount === amt ? "bg-sky-400 text-white border-sky-400 shadow-lg scale-105" : "bg-[#001a2c] border-[#003a4c] text-white hover:bg-sky-500 hover:text-white hover:scale-105"}`}
              onClick={() => setSelectedAmount(amt)}
            >
              {amountMap[amt]}
            </button>
          ))}
        </div>
        {/* Discount Bar */}
        <div className="w-full flex justify-center mb-12">
          <div className="bg-gradient-to-r from-sky-500 to-sky-400 text-white font-extrabold px-12 py-5 rounded-2xl shadow-2xl text-2xl flex items-center gap-4 tracking-wide border-2 border-sky-400">
            {discount} <span className="ml-4 text-base font-mono bg-[#001a2c] text-sky-200 px-4 py-2 rounded-xl border border-sky-400">CODE: SUMMER30</span>
          </div>
        </div>
        {/* Main Content */}
        <div className="max-w-7xl w-full mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Plan Details Card */}
          <div className="bg-gradient-to-br from-[#001a2c] via-[#002a3c] to-[#003a4c] rounded-2xl shadow-2xl p-12 space-y-8 border-2 border-sky-900/60 text-xl relative overflow-hidden">
            <div className="absolute inset-0 pointer-events-none rounded-2xl border-2 border-sky-500/20" style={{boxShadow: '0 0 60px 0 #38bdf8aa, 0 2px 16px 0 #0008'}}></div>
            <h3 className="text-3xl font-extrabold mb-8 text-sky-400 tracking-wide flex items-center gap-3">
              {selectedStep} <span className="text-white ml-4">{planDetails.label}</span>
            </h3>
            <div className="space-y-6">
              <div className="flex justify-between border-b-2 border-[#003a4c] pb-4"><span>🎯 Target</span><span className="font-extrabold text-sky-400">{planDetails.target}</span></div>
              <div className="flex justify-between border-b-2 border-[#003a4c] pb-4"><span>📉 Daily Drawdown</span><span className="font-extrabold text-sky-400">{planDetails.dailyDrawdown}</span></div>
              <div className="flex justify-between border-b-2 border-[#003a4c] pb-4"><span>📉 Maximum Drawdown</span><span className="font-extrabold text-sky-400">{planDetails.maxDrawdown}</span></div>
              <div className="flex justify-between border-b-2 border-[#003a4c] pb-4"><span>📅 Min. Trading Days</span><span className="font-extrabold text-sky-400">{planDetails.minTradingDays}</span></div>
              <div className="flex justify-between border-b-2 border-[#003a4c] pb-4"><span>⏰ Reward Schedule</span><span className="font-extrabold text-sky-400">{planDetails.rewardSchedule}</span></div>
              <div className="flex justify-between border-b-2 border-[#003a4c] pb-4"><span>⚖️ Leverage</span><span className="font-extrabold text-sky-400">{planDetails.leverage}</span></div>
              <div className="flex justify-between"><span>💰 Profit Split</span><span className="font-extrabold text-sky-400">{planDetails.profitSplit}</span></div>
            </div>
          </div>
          {/* Summary/Checkout Card */}
          <div className="relative bg-gradient-to-br from-[#001a2c] via-[#003a4c] to-[#002a3c] rounded-2xl shadow-2xl p-12 flex flex-col items-center border-2 border-sky-900/60 overflow-hidden">
            <div className="absolute inset-0 pointer-events-none rounded-2xl border-2 border-sky-400/30" style={{boxShadow: '0 0 80px 0 #38bdf8cc, 0 2px 24px 0 #000a'}}></div>
            <div className="text-5xl font-extrabold text-sky-400 mb-2 tracking-tight drop-shadow-lg">${discountPrice}</div>
            <div className="text-2xl line-through text-gray-400 mb-2">${realPrice}</div>
            <div className="text-2xl text-sky-400 mb-6 font-bold tracking-wide">{amountMap[selectedAmount]} CHALLENGE</div>
            <button className="w-full bg-gradient-to-r from-sky-500 to-sky-400 text-white font-extrabold py-5 rounded-2xl text-2xl shadow-xl hover:bg-sky-600 hover:scale-105 transition mb-6 tracking-wide focus:outline-none focus:ring-2 focus:ring-sky-400 focus:ring-offset-2 focus:ring-offset-[#001a2c] duration-200">
              {cta}
            </button>
            <div className="text-lg text-gray-400 mb-8 flex items-center gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="underline decoration-dotted cursor-help">ONE-TIME 100% REFUNDABLE FEE</span>
                </TooltipTrigger>
                <TooltipContent className="bg-[#001a2c] border border-sky-400 text-white text-base max-w-xs">
                  If you meet the profit target and rules, your fee is fully refunded. No hidden charges.
                </TooltipContent>
              </Tooltip>
            </div>
            <div className="flex flex-wrap justify-center gap-8 mb-2 mt-4">
              {paymentMethods.map((pm, i) => (
                <div key={i} className="flex flex-col items-center text-sky-400 text-4xl">
                  <span className="flex items-center justify-center w-14 h-14 rounded-full bg-[#002a3c] border border-sky-900/40 shadow-inner mb-2">{pm.icon}</span>
                  <span className="text-base text-white font-semibold">{pm.label}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>
    </TooltipProvider>
  )
}
