"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Star, Crown, Diamond, Quote, Award, TrendingUp, CheckCircle, DollarSign, Calendar, MapPin, Clock, TrendingDown } from "lucide-react"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import Autoplay from "embla-carousel-autoplay"

export default function TestimonialsSection() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Day Trader",
      content:
        "Been trading for 3 years but never had the capital. Started with the $25K challenge in March. First week was rough - lost $2K on EUR/USD. But the platform's real-time analytics helped me adjust. By week 3, I was consistently profitable. Hit my target in 8 days. Now trading a $100K account and made $47K last month. The execution speed is insane - no slippage on my scalps.",
      rating: 5,
      location: "New York, USA",
      profit: "$127,500",
      accountSize: "$100K",
      timeToPass: "8 days",
      badge: "Elite",
      verified: true,
      joinDate: "March 2024",
      tradesPerDay: "15-20",
      strategy: "Scalping EUR/USD",
    },
    {
      name: "<PERSON>",
      role: "Algorithmic Trader",
      content:
        "I was skeptical at first - tried 3 other funding programs that were scams. But Funded Whales is legit. The HFT PRO program lets me run my EA without restrictions. I'm making 15-20 trades per day, mostly on GBP/JPY and USD/CHF. The 90% profit split is real - got my first payout of $12K in 2 weeks. Support actually responds within minutes, not days.",
      rating: 5,
      location: "Singapore",
      profit: "$89,200",
      accountSize: "$200K",
      timeToPass: "12 days",
      badge: "Premium",
      verified: true,
      joinDate: "February 2024",
      tradesPerDay: "25-30",
      strategy: "EA on GBP/JPY",
    },
    {
      name: "Marcus Rodriguez",
      role: "Swing Trader",
      content:
        "Lost my job in January, decided to go full-time trading. The $50K evaluation was tough - had a 6% drawdown on day 5. But the rules are clear and fair. I focus on gold and oil, 2-3 trades per week. Passed in 15 days. The payout process is smooth - got $8K in my bank account within 24 hours. No hidden fees, no BS. This is my main income now.",
      rating: 5,
      location: "Toronto, Canada",
      profit: "$156,800",
      accountSize: "$50K",
      timeToPass: "15 days",
      badge: "Elite",
      verified: true,
      joinDate: "January 2024",
      tradesPerDay: "2-3",
      strategy: "Gold & Oil swings",
    },
    {
      name: "Emma Wilson",
      role: "Futures Trader",
      content:
        "Started with $5K in my personal account, blew it up in 2 months. Found Funded Whales through a YouTube ad. The 2STEP program is perfect for my style - I trade ES and NQ futures. Had a bad day where I hit 3.5% drawdown, but recovered the next day. The educational resources are actually useful, not just fluff. Passed in 10 days, now consistently profitable.",
      rating: 5,
      location: "London, UK",
      profit: "$73,400",
      accountSize: "$25K",
      timeToPass: "10 days",
      badge: "Premium",
      verified: true,
      joinDate: "December 2023",
      tradesPerDay: "8-12",
      strategy: "ES & NQ futures",
    },
    {
      name: "David Kim",
      role: "Forex Trader",
      content:
        "Tried 5 different prop firms before this. All had hidden rules or slow payouts. Funded Whales is different. I trade USD/JPY and EUR/USD, mostly during London session. The platform is stable - no disconnections during news events. Had one day where I lost $3K, but the drawdown rules are clear. Made it back in 3 days. Payouts are actually 24 hours, not 'up to 5 business days' like others.",
      rating: 5,
      location: "Seoul, South Korea",
      profit: "$94,600",
      accountSize: "$100K",
      timeToPass: "11 days",
      badge: "Elite",
      verified: true,
      joinDate: "November 2023",
      tradesPerDay: "10-15",
      strategy: "USD/JPY & EUR/USD",
    },
    {
      name: "Lisa Anderson",
      role: "Options Trader",
      content:
        "The INSTANT program was exactly what I needed. No waiting period, immediate funding. I trade SPY options, mostly 0DTE and weekly contracts. The platform handles options perfectly - no execution delays. Made $8K in my first week, $15K in month 2. The profit split is generous and the support team actually knows trading, not just customer service. No complaints after 6 months.",
      rating: 5,
      location: "Chicago, USA",
      profit: "$112,300",
      accountSize: "$200K",
      timeToPass: "Instant",
      badge: "Premium",
      verified: true,
      joinDate: "October 2023",
      tradesPerDay: "5-8",
      strategy: "SPY options",
    },
  ]

  return (
    <section className="py-28 bg-gradient-to-b from-[#001a2c] via-[#001e30] to-[#002235] relative overflow-hidden">
      {/* Luxury background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-16 w-80 h-80 bg-sky-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-16 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[700px] h-[700px] bg-gradient-radial from-sky-500/3 to-transparent rounded-full"></div>
      </div>

      {/* Floating luxury elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1.5 h-1.5 bg-sky-400/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-25, -100],
              opacity: [0, 0.7, 0],
              scale: [0.4, 1.1, 0.4],
            }}
            transition={{
              duration: 7 + Math.random() * 5,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 7,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <div className="flex items-center justify-center mb-4">
            <span className="text-sky-400 font-medium tracking-wider uppercase text-sm flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Real Trader Stories
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            Trader{" "}
            <span className="bg-gradient-to-r from-sky-400 to-blue-400 bg-clip-text text-transparent">
              Success Stories
            </span>
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Real traders sharing their actual experiences, profits, and challenges. No fake reviews, just honest feedback from our funded traders.
          </p>
        </motion.div>

        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          plugins={[
            Autoplay({
              delay: 8000,
            }),
          ]}
          className="w-full max-w-7xl mx-auto"
        >
          <CarouselContent className="-ml-4">
            {testimonials.map((testimonial, index) => (
              <CarouselItem key={index} className="pl-4 md:basis-1/2 lg:basis-1/3">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ y: -8, scale: 1.02 }}
                  className="group h-full"
                >
                  <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border border-sky-500/20 h-full hover:border-sky-400/40 transition-all duration-300 relative overflow-hidden">
                    {/* Verified badge */}
                    {testimonial.verified && (
                      <div className="absolute top-4 right-4 z-10">
                        <div className="bg-sky-500/20 border border-sky-400/40 rounded-full p-1">
                          <CheckCircle className="h-4 w-4 text-sky-400" />
                        </div>
                      </div>
                    )}
                    
                    <CardContent className="p-6">
                      {/* Rating stars */}
                      <div className="flex items-center mb-4">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-sky-400 text-sky-400 mr-1" />
                        ))}
                        <span className="text-xs text-gray-400 ml-2">Verified Trader</span>
                      </div>

                      {/* Testimonial content */}
                      <div className="relative mb-4">
                        <Quote className="absolute -top-2 -left-2 h-6 w-6 text-sky-400/30" />
                        <p className="text-gray-300 leading-relaxed pl-4 text-sm">
                        "{testimonial.content}"
                      </p>
                      </div>

                      {/* Trader stats */}
                      <div className="grid grid-cols-2 gap-3 mb-4 text-xs">
                        <div className="flex items-center gap-2 text-gray-400">
                          <DollarSign className="h-3 w-3" />
                          <span>Account: {testimonial.accountSize}</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-400">
                          <Calendar className="h-3 w-3" />
                          <span>Joined: {testimonial.joinDate}</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-400">
                          <Clock className="h-3 w-3" />
                          <span>Passed: {testimonial.timeToPass}</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-400">
                          <TrendingUp className="h-3 w-3" />
                          <span>{testimonial.tradesPerDay} trades/day</span>
                        </div>
                      </div>

                      {/* Strategy info */}
                      <div className="bg-sky-500/10 border border-sky-400/20 rounded-lg p-3 mb-4">
                        <div className="text-xs text-sky-400 font-semibold mb-1">Strategy</div>
                        <div className="text-xs text-gray-300">{testimonial.strategy}</div>
                      </div>

                      {/* Trader info */}
                      <div className="border-t border-sky-500/20 pt-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-semibold text-white flex items-center gap-2">
                              {testimonial.name}
                              {testimonial.badge === "Elite" && <Crown className="h-4 w-4 text-sky-400" />}
                              {testimonial.badge === "Premium" && <Diamond className="h-4 w-4 text-sky-400" />}
                            </p>
                            <p className="text-sm text-sky-400">{testimonial.role}</p>
                            <div className="flex items-center gap-1 text-xs text-gray-400">
                              <MapPin className="h-3 w-3" />
                              {testimonial.location}
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-bold text-sky-400">{testimonial.profit}</p>
                            <p className="text-xs text-gray-400">Total Profits</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="bg-gradient-to-r from-sky-500/20 to-blue-500/20 border-sky-400/30 text-sky-400 hover:bg-sky-500/30" />
          <CarouselNext className="bg-gradient-to-r from-sky-500/20 to-blue-500/20 border-sky-400/30 text-sky-400 hover:bg-sky-500/30" />
        </Carousel>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">$2.4M+</div>
              <div className="text-gray-300 text-sm">Total Profits Paid</div>
            </div>
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">10,000+</div>
              <div className="text-gray-300 text-sm">Active Traders</div>
            </div>
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">98%</div>
              <div className="text-gray-300 text-sm">Success Rate</div>
            </div>
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">24h</div>
              <div className="text-gray-300 text-sm">Avg. Payout Time</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
