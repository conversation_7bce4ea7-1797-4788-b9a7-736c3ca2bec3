"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, EyeOff, Lock, Mail, User, Shield, ArrowRight, Github, CheckCircle, TrendingUp, Users, DollarSign, BarChart3, Globe, Zap, Target, Award, Clock, Phone, MapPin, Globe as GlobeIcon } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { signup } from "@/lib/api"

export default function SignUpPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    name: "",
    phone_no: "",
    country: "",
    address: "",
    referral_code: "",
    agreeToTerms: false
  })

  const [marketData, setMarketData] = useState({
    activeTraders: "12,847",
    totalVolume: "$2.4B",
    successRate: "94.2%",
    avgProfit: "$1,247",
    newTraders: "1,234",
    avgTimeToProfit: "3.2 months"
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    })
    setError("") // Clear error when user starts typing
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      // Validate required fields
      if (!formData.username || !formData.email || !formData.password || !formData.name || 
          !formData.phone_no || !formData.country || !formData.address) {
        throw new Error("Please fill in all required fields")
      }

      const signupData = {
        username: formData.username,
        email: formData.email,
        password: formData.password,
        name: formData.name,
        phone_no: formData.phone_no,
        country: formData.country,
        address: formData.address,
        referral_code: formData.referral_code || undefined
      }

      await signup(signupData)
      router.push("/auth/login?message=Account created successfully! Please log in.")
    } catch (error) {
      setError(error instanceof Error ? error.message : "Signup failed. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  }

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0
    })
  }

  const accountTypes = [
    { value: "individual", label: "Individual Trader" },
    { value: "professional", label: "Professional Trader" },
    { value: "institutional", label: "Institutional" }
  ]

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-[#001a2c] to-[#000c14] relative overflow-hidden">
      {/* Background Trading Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-sky-500/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-500/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-purple-500/20 rounded-full blur-2xl"></div>
        <div className="absolute top-1/3 right-1/4 w-20 h-20 bg-green-500/20 rounded-full blur-2xl"></div>
      </div>

      <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        {/* Left Side - Trading Stats & Info */}
        <motion.div
          className="hidden lg:block"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="space-y-8">
            <motion.div variants={itemVariants}>
              <h1 className="text-5xl font-bold text-white mb-4">
                Start Your <span className="text-sky-400">Trading Journey</span>
              </h1>
              <p className="text-xl text-gray-300 mb-8">
                Join thousands of successful traders and access the world's most advanced 
                trading platform. Your path to financial freedom starts here.
              </p>
            </motion.div>

            {/* Trading Statistics */}
            <motion.div variants={itemVariants} className="grid grid-cols-2 gap-6">
              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-sky-500/20 rounded-lg flex items-center justify-center">
                    <Users className="h-5 w-5 text-sky-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.activeTraders}</p>
                    <p className="text-sm text-gray-400">Active Traders</p>
                  </div>
                </div>
              </div>

              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <DollarSign className="h-5 w-5 text-green-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.totalVolume}</p>
                    <p className="text-sm text-gray-400">Total Volume</p>
                  </div>
                </div>
              </div>

              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <TrendingUp className="h-5 w-5 text-purple-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.successRate}</p>
                    <p className="text-sm text-gray-400">Success Rate</p>
                  </div>
                </div>
              </div>

              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center">
                    <BarChart3 className="h-5 w-5 text-orange-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.avgProfit}</p>
                    <p className="text-sm text-gray-400">Avg Profit</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Platform Benefits */}
            <motion.div variants={itemVariants} className="space-y-6">
              <h3 className="text-xl font-semibold text-white mb-4">Why Choose Our Platform?</h3>
              <div className="grid grid-cols-1 gap-4">
                <div className="flex items-center space-x-3 text-gray-300">
                  <div className="w-8 h-8 bg-sky-500/20 rounded-lg flex items-center justify-center">
                    <Target className="h-4 w-4 text-sky-400" />
                  </div>
                  <span>Proven trading strategies & expert guidance</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300">
                  <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <Award className="h-4 w-4 text-green-400" />
                  </div>
                  <span>Industry-leading success rates & profitability</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300">
                  <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <Clock className="h-4 w-4 text-purple-400" />
                  </div>
                  <span>24/7 market access & real-time execution</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300">
                  <div className="w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center">
                    <Shield className="h-4 w-4 text-orange-400" />
                  </div>
                  <span>Bank-grade security & fund protection</span>
                </div>
              </div>
            </motion.div>

            {/* Recent Success Stories */}
            <motion.div variants={itemVariants} className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
              <h4 className="text-lg font-semibold text-white mb-4">Recent Success Stories</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">New trader Sarah M.</span>
                  <span className="text-green-400 font-semibold">+$2,847 profit</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">Professional trader Alex K.</span>
                  <span className="text-green-400 font-semibold">+$15,234 profit</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">Institutional client</span>
                  <span className="text-green-400 font-semibold">+$89,456 profit</span>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Right Side - Signup Form */}
        <motion.div
          className="w-full max-w-md mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Card className="bg-gradient-to-br from-[#002a3c] to-[#001a2c] border-[#004c66] shadow-2xl overflow-hidden">
            <CardHeader className="space-y-4 pb-6">
              <motion.div variants={itemVariants}>
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-sky-400 to-blue-600 flex items-center justify-center mb-4 mx-auto">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <CardTitle className="text-2xl font-bold text-center text-white">Create Account</CardTitle>
                <CardDescription className="text-center text-gray-400">
                  Join our trading platform in a few steps
                </CardDescription>
              </motion.div>

              <div className="flex justify-center space-x-2 pt-4">
                {[1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className={`h-2 rounded-full ${
                      i === step ? "w-8 bg-sky-500" : "w-2 bg-[#004c66]"
                    }`}
                    animate={{ backgroundColor: i === step ? "#0ea5e9" : "#004c66" }}
                    transition={{ duration: 0.3 }}
                  />
                ))}
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              <motion.div
                key={step}
                custom={step}
                variants={slideVariants}
                initial="enter"
                animate="center"
                exit="exit"
                transition={{
                  x: { type: "spring", stiffness: 300, damping: 30 },
                  opacity: { duration: 0.2 }
                }}
              >
                {error && (
                  <motion.div variants={itemVariants} className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                    <p className="text-red-400 text-sm">{error}</p>
                  </motion.div>
                )}

                {step === 1 ? (
                  <div className="space-y-4">
                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="username" className="text-white">Username</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="username"
                          value={formData.username}
                          onChange={(e) => handleInputChange("username", e.target.value)}
                          placeholder="Enter your username"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="name" className="text-white">Full Name</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                          placeholder="Enter your full name"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="email" className="text-white">Email</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange("email", e.target.value)}
                          placeholder="Enter your email"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="password" className="text-white">Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="password"
                          type={showPassword ? "text" : "password"}
                          value={formData.password}
                          onChange={(e) => handleInputChange("password", e.target.value)}
                          placeholder="Create a strong password"
                          className="pl-10 pr-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 top-0 h-12 w-12 text-gray-400 hover:text-white"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                        </Button>
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants}>
                      <Button
                        onClick={() => setStep(2)}
                        className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium"
                      >
                        Continue
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </motion.div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="phone_no" className="text-white">Phone Number</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="phone_no"
                          value={formData.phone_no}
                          onChange={(e) => handleInputChange("phone_no", e.target.value)}
                          placeholder="Enter your phone number"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="country" className="text-white">Country</Label>
                      <div className="relative">
                        <GlobeIcon className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="country"
                          value={formData.country}
                          onChange={(e) => handleInputChange("country", e.target.value)}
                          placeholder="Enter your country"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="address" className="text-white">Address</Label>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="address"
                          value={formData.address}
                          onChange={(e) => handleInputChange("address", e.target.value)}
                          placeholder="Enter your address"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="referral_code" className="text-white">Referral Code (Optional)</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="referral_code"
                          value={formData.referral_code}
                          onChange={(e) => handleInputChange("referral_code", e.target.value)}
                          placeholder="Enter referral code (optional)"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="terms"
                          checked={formData.agreeToTerms}
                          onCheckedChange={(checked) =>
                            setFormData({ ...formData, agreeToTerms: checked as boolean })
                          }
                          className="border-[#004c66] data-[state=checked]:bg-sky-500"
                        />
                        <label
                          htmlFor="terms"
                          className="text-sm text-gray-400"
                        >
                          I agree to the{" "}
                          <Link href="/terms" className="text-sky-400 hover:text-sky-300">
                            Terms of Service
                          </Link>{" "}
                          and{" "}
                          <Link href="/privacy" className="text-sky-400 hover:text-sky-300">
                            Privacy Policy
                          </Link>
                        </label>
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-3">
                      <Button
                        onClick={handleSignUp}
                        disabled={!formData.agreeToTerms || isLoading}
                        className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium"
                      >
                        {isLoading ? (
                          <motion.div
                            className="h-5 w-5 border-2 border-white border-t-transparent rounded-full"
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          />
                        ) : (
                          <>
                            Create Account
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </>
                        )}
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        onClick={() => setStep(1)}
                        className="w-full text-gray-400 hover:text-white"
                      >
                        Back to previous step
                      </Button>
                    </motion.div>
                  </div>
                )}
              </motion.div>

              <motion.div variants={itemVariants}>
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-[#004c66]"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 text-gray-400 bg-[#001a2c]">Or sign up with</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mt-6">
                  <Button 
                    variant="outline" 
                    className="w-full border-[#004c66] text-white hover:bg-[#003a4c] space-x-2"
                  >
                    <span className="h-4 w-4">G</span>
                    <span>Google</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full border-[#004c66] text-white hover:bg-[#003a4c] space-x-2"
                  >
                    <Github className="h-4 w-4" />
                    <span>GitHub</span>
                  </Button>
                </div>
              </motion.div>
            </CardContent>

            <CardFooter>
              <motion.p variants={itemVariants} className="text-center text-gray-400 text-sm w-full">
                Already have an account?{" "}
                <Link href="/auth/login" className="text-sky-400 hover:text-sky-300 font-medium transition-colors">
                  Sign In
                </Link>
              </motion.p>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </div>
  )
} 