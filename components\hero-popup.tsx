"use client"
import { useState } from "react"
import { X } from "lucide-react"
import Image from "next/image"

export default function HeroPopup() {
  const [open, setOpen] = useState(true)
  const [firstName, setFirstName] = useState("")
  const [email, setEmail] = useState("")

  if (!open) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
      <div className="relative w-full max-w-2xl mx-auto rounded-2xl overflow-hidden shadow-2xl flex flex-col md:flex-row bg-gradient-to-br from-[#001a2c] via-[#002a3c] to-[#002235]">
        {/* Left: Branding/Visual */}
        <div className="hidden md:flex flex-col items-center justify-center w-1/2 bg-gradient-to-br from-sky-900/80 to-blue-900/80 p-8 relative">
          <div className="flex flex-col items-center w-full">
            <Image
              src="https://res.cloudinary.com/dufcjjaav/image/upload/v1751695664/logo-removebg-preview_eqaddm.png"
              alt="Funded Whales Logo"
              width={60}
              height={60}
              className="mb-6"
              priority
            />
            <div className="text-white text-2xl font-semibold mb-2 tracking-wide">FundingPips<span className="align-super text-xs ml-1">®</span></div>
            <div className="text-3xl font-bold text-white mb-4 tracking-wide text-center leading-tight">Built By Traders<br />For Traders</div>
            <div className="mt-8 text-gray-200 font-medium text-sm tracking-wider">fundingpips.com</div>
          </div>
        </div>
        {/* Right: Form */}
        <div className="flex-1 bg-[#001a2c] p-8 flex flex-col justify-center relative">
          <button
            className="absolute top-4 right-4 text-gray-400 hover:text-white text-2xl"
            onClick={() => setOpen(false)}
            aria-label="Close"
          >
            <X className="w-7 h-7" />
          </button>
          <div className="text-center md:text-left">
            <div className="text-xl md:text-2xl font-bold text-white mb-2 leading-tight">
              You haven't joined the FundedWhales Community yet?
            </div>
            <div className="text-gray-300 mb-6 text-base">
              Sign up now and unlock all the newest promotions, newsletter and updates
            </div>
            <form className="space-y-4">
              <input
                type="text"
                placeholder="First Name"
                value={firstName}
                onChange={e => setFirstName(e.target.value)}
                className="w-full rounded-lg bg-[#002235] border border-sky-500/30 text-white placeholder:text-gray-400 px-4 py-3 focus:outline-none focus:border-sky-400 transition"
              />
              <input
                type="email"
                placeholder="Email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                className="w-full rounded-lg bg-[#002235] border border-sky-500/30 text-white placeholder:text-gray-400 px-4 py-3 focus:outline-none focus:border-sky-400 transition"
              />
              <button
                type="submit"
                className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white font-bold text-lg py-3 rounded-lg mt-2 shadow-lg transition-all"
              >
                Sign Me Up!
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
} 