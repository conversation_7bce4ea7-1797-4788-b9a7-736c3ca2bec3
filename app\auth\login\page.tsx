"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Eye, EyeOff, Lock, Mail, Shield, ArrowRight, Github, TrendingUp, Users, DollarSign, BarChart3, Globe, Zap } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { login, saveToken } from "@/lib/api"
import { useAuth } from "@/lib/auth-context"

export default function LoginPage() {
  const router = useRouter()
  const { setToken } = useAuth()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [successMessage, setSuccessMessage] = useState("")
  const [formData, setFormData] = useState({
    username: "",
    password: ""
  })
  const [marketData, setMarketData] = useState({
    activeTraders: "12,847",
    totalVolume: "$2.4B",
    successRate: "94.2%",
    avgProfit: "$1,247"
  })

  // Check for success message from URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const message = urlParams.get('message')
    if (message) {
      setSuccessMessage(message)
      // Clear the message from URL
      window.history.replaceState({}, document.title, window.location.pathname)
    }
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError("") // Clear error when user starts typing
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const response = await login(formData)
      saveToken(response.access_token)
      setToken(response.access_token)
      router.push("/dashboard")
    } catch (error) {
      setError(error instanceof Error ? error.message : "Login failed. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-[#001a2c] to-[#000c14] relative overflow-hidden">
      {/* Background Trading Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-sky-500/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-500/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-purple-500/20 rounded-full blur-2xl"></div>
      </div>

      <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        {/* Left Side - Trading Stats & Info */}
        <motion.div
          className="hidden lg:block"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="space-y-8">
            <motion.div variants={itemVariants}>
              <h1 className="text-5xl font-bold text-white mb-4">
                Welcome to <span className="text-sky-400">Professional Trading</span>
              </h1>
              <p className="text-xl text-gray-300 mb-8">
                Access your account and start trading with the world's most advanced platform. 
                Join thousands of successful traders worldwide.
              </p>
            </motion.div>

            {/* Trading Statistics */}
            <motion.div variants={itemVariants} className="grid grid-cols-2 gap-6">
              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-sky-500/20 rounded-lg flex items-center justify-center">
                    <Users className="h-5 w-5 text-sky-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.activeTraders}</p>
                    <p className="text-sm text-gray-400">Active Traders</p>
                  </div>
                </div>
              </div>

              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <DollarSign className="h-5 w-5 text-green-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.totalVolume}</p>
                    <p className="text-sm text-gray-400">Total Volume</p>
                  </div>
                </div>
              </div>

              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <TrendingUp className="h-5 w-5 text-purple-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.successRate}</p>
                    <p className="text-sm text-gray-400">Success Rate</p>
                  </div>
                </div>
              </div>

              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center">
                    <BarChart3 className="h-5 w-5 text-orange-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.avgProfit}</p>
                    <p className="text-sm text-gray-400">Avg Profit</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Platform Features */}
            <motion.div variants={itemVariants} className="space-y-4">
              <h3 className="text-xl font-semibold text-white mb-4">Platform Features</h3>
              <div className="grid grid-cols-1 gap-3">
                <div className="flex items-center space-x-3 text-gray-300">
                  <Zap className="h-5 w-5 text-sky-400" />
                  <span>Real-time market data & advanced charting</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300">
                  <Globe className="h-5 w-5 text-sky-400" />
                  <span>Global market access 24/7</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300">
                  <Shield className="h-5 w-5 text-sky-400" />
                  <span>Bank-grade security & encryption</span>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Right Side - Login Form */}
        <motion.div
          className="w-full max-w-md mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Card className="bg-gradient-to-br from-[#002a3c] to-[#001a2c] border-[#004c66] shadow-2xl">
            <CardHeader className="space-y-4 pb-6">
              <motion.div variants={itemVariants}>
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-sky-400 to-blue-600 flex items-center justify-center mb-4 mx-auto">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <CardTitle className="text-2xl font-bold text-center text-white">Welcome Back</CardTitle>
                <CardDescription className="text-center text-gray-400">
                  Access your trading account and continue your journey
                </CardDescription>
              </motion.div>
            </CardHeader>
            <CardContent className="space-y-6">
              <motion.div variants={itemVariants}>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <Button 
                    variant="outline" 
                    className="w-full border-[#004c66] text-white hover:bg-[#003a4c] space-x-2"
                  >
                    <span className="h-4 w-4">G</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full border-[#004c66] text-white hover:bg-[#003a4c] space-x-2"
                  >
                    <Github className="h-4 w-4" />
                    <span>GitHub</span>
                  </Button>
                </div>

                <div className="relative mb-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-[#004c66]"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 text-gray-400 bg-[#001a2c]">Or continue with</span>
                  </div>
                </div>
              </motion.div>

              {error && (
                <motion.div variants={itemVariants} className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                  <p className="text-red-400 text-sm">{error}</p>
                </motion.div>
              )}

              {successMessage && (
                <motion.div variants={itemVariants} className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                  <p className="text-green-400 text-sm">{successMessage}</p>
                </motion.div>
              )}

              <form onSubmit={handleLogin} className="space-y-4">
                <motion.div variants={itemVariants} className="space-y-2">
                  <Label htmlFor="username" className="text-white">Username</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <Input
                      id="username"
                      name="username"
                      type="text"
                      placeholder="Enter your username"
                      className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                      value={formData.username}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </motion.div>

                <motion.div variants={itemVariants} className="space-y-2">
                  <Label htmlFor="password" className="text-white">Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      className="pl-10 pr-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-12 w-12 text-gray-400 hover:text-white"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </Button>
                  </div>
                </motion.div>

                <motion.div variants={itemVariants} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="remember" className="border-[#004c66] data-[state=checked]:bg-sky-500" />
                    <label
                      htmlFor="remember"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-400"
                    >
                      Remember me
                    </label>
                  </div>
                  <Link
                    href="/auth/reset-password"
                    className="text-sm font-medium text-sky-400 hover:text-sky-300 transition-colors"
                  >
                    Forgot password?
                  </Link>
                </motion.div>

                <motion.div variants={itemVariants}>
                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <motion.div
                        className="h-5 w-5 border-2 border-white border-t-transparent rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                    ) : (
                      <>
                        Sign In
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </motion.div>
              </form>
            </CardContent>
            <CardFooter>
              <motion.p variants={itemVariants} className="text-center text-gray-400 text-sm w-full">
                Don't have an account?{" "}
                <Link href="/auth/signup" className="text-sky-400 hover:text-sky-300 font-medium transition-colors">
                  Create Account
                </Link>
              </motion.p>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </div>
  )
} 
