"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Upload, 
  QrCode, 
  CreditCard, 
  CheckCircle, 
  DollarSign, 
  Shield, 
  Clock, 
  Award, 
  Loader2,
  Zap,
  Target,
  TrendingUp,
  Star,
  Lock,
  Globe,
  Activity,
  Users,
  BarChart3,
  Sparkles,
  Crown,
  Rocket,
  Trophy,
  Diamond,
  Flame,
  Lightning,
  Eye,
  EyeOff,
  ArrowRight,
  ArrowLeft,
  Check,
  X,
  AlertTriangle,
  Info,
  Play,
  Pause,
  RotateCcw,
  Mail,
  Calendar,
  Timer,
  Percent,
  TrendingDown,
  AlertCircle,
  ExternalLink,
  Download,
  Co<PERSON>,
  Settings,
  HelpCircle
} from "lucide-react"
import { createOrder } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"

export default function BuyAccountPage() {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    email: "",
    challengeType: "",
    platform: "",
    size: "",
    paymentMethod: "",
    txid: "",
    proofImage: null as File | null,
  })
  
  const [showPayment, setShowPayment] = useState(false)
  const [orderPlaced, setOrderPlaced] = useState(false)
  const [selectedPrice, setSelectedPrice] = useState<number>(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [showPassword, setShowPassword] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([])

  const challengeTypes = [
    {
      value: "instant",
      label: "Instant",
      description: "Instant Funding Challenge",
      features: ["No evaluation", "Immediate access", "Fastest payout"],
      icon: <Zap className="h-8 w-8" />,
      color: "from-yellow-400 via-orange-500 to-red-500",
      badge: "Fastest",
      price: 0, // Will be set per size
      popular: true,
      featuresList: ["No evaluation", "Immediate access", "Fast payout", "All strategies allowed"],
      duration: "Instant",
      profitTarget: "None",
      maxDrawdown: "Standard"
    },
    { 
      value: "hft",
      label: "HFT",
      description: "High-Frequency Trading Challenge",
      features: ["Unlimited trading time", "Advanced algorithms allowed", "API trading supported", "Real-time data feeds", "Custom indicators"],
      icon: <TrendingUp className="h-8 w-8" />,
      color: "from-purple-600 via-pink-600 to-red-600",
      badge: "Premium",
      price: 0, // Will be set per size
      popular: false,
      featuresList: ["Unlimited trading time", "Advanced algorithms", "API trading", "Real-time feeds", "Custom indicators", "Priority support"],
      duration: "Unlimited",
      profitTarget: "Flexible",
      maxDrawdown: "Custom"
    },
    { 
      value: "one-step",
      label: "One-Step",
      description: "One-Step Evaluation Challenge",
      features: ["Simple rules", "Quick evaluation", "Low cost"],
      icon: <Target className="h-8 w-8" />,
      color: "from-sky-600 via-blue-600 to-indigo-600",
      badge: "Popular",
      price: 0, // Will be set per size
      popular: false,
      featuresList: ["Simple rules", "Quick evaluation", "Low cost", "Basic support"],
      duration: "14 Days",
      profitTarget: "10%",
      maxDrawdown: "8%"
    },
    { 
      value: "two-step",
      label: "Two-Step",
      description: "Two-Step Evaluation Challenge",
      features: ["Standard evaluation", "Best for beginners"],
      icon: <Award className="h-8 w-8" />,
      color: "from-green-600 via-emerald-600 to-teal-600",
      badge: "Standard",
      price: 0, // Will be set per size
      popular: false,
      featuresList: ["Standard evaluation", "Best for beginners", "Support included"],
      duration: "30 Days",
      profitTarget: "5%",
      maxDrawdown: "4%"
    },
  ]

  const platforms = [
    { value: "mt4", label: "MetaTrader 4", icon: "🔷", description: "Classic trading platform", features: ["User-friendly interface", "Extensive indicators", "EA support"] },
    { value: "mt5", label: "MetaTrader 5", icon: "🔶", description: "Advanced trading platform", features: ["Advanced charting", "More timeframes", "Better backtesting"] },
  ]

  const sizes = [
    { value: "1000", label: "$1,000" },
    { value: "3000", label: "$3,000" },
    { value: "5000", label: "$5,000" },
    { value: "10000", label: "$10,000" },
    { value: "25000", label: "$25,000" },
    { value: "50000", label: "$50,000" },
    { value: "100000", label: "$100,000" },
    { value: "200000", label: "$200,000" },
    { value: "500000", label: "$500,000" },
  ]

  const paymentMethods = [
    { value: "usdt-trc20", label: "USDT (TRC20)", qr: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwMCIvPjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjE2MCIgaGVpZ2h0PSIxNjAiIGZpbGw9IiNmZmYiLz48L3N2Zz4=", network: "TRON", fee: "0%", speed: "Instant" },
    { value: "usdt-erc20", label: "USDT (ERC20)", qr: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwMCIvPjxyZWN0IHg9IjMwIiB5PSIzMCIgd2lkdGg9IjE0MCIgaGVpZ2h0PSIxNDAiIGZpbGw9IiNmZmYiLz48L3N2Zz4=", network: "Ethereum", fee: "0%", speed: "2-5 min" },
    { value: "bitcoin", label: "Bitcoin", qr: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwMCIvPjxyZWN0IHg9IjQwIiB5PSI0MCIgd2lkdGg9IjEyMCIgaGVpZ2h0PSIxMjAiIGZpbGw9IiNmZmYiLz48L3N2Zz4=", network: "Bitcoin", fee: "0%", speed: "10-30 min" },
  ]

  const priceTable = {
    instant: {
      real:   [56, 131, 231, 419, 883, 1930, 3430, 5650, 11230],
      discount: [45, 105, 185, 335, 706, 772, 1372, 2260, 4492],
    },
    hft: {
      real:   [18, 35, 47, 80, 175, 290, 510, 819, 1490],
      discount: [14, 28, 38, 64, 140, 116, 204, 328, 596],
    },
    'one-step': {
      real:   [10, 16, 28, 48, 92, 167, 248, 425, 875],
      discount: [8, 13, 22, 38, 74, 67, 99, 170, 350],
    },
    'two-step': {
      real:   [8, 14, 25, 33, 69, 125, 188, 313, 985],
      discount: [6, 11, 20, 26, 55, 50, 75, 125, 394],
    },
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (field === "paymentMethod" && value) {
      setShowPayment(true)
    }
    if (field === "size") {
      const selectedSize = sizes.find(s => s.value === value)
      setSelectedPrice(selectedSize?.price || 0)
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setFormData(prev => ({ ...prev, proofImage: file }))
    }
  }

  const handlePlaceOrder = async () => {
    if (!formData.email || !formData.challengeType || 
        !formData.platform || !formData.size || !formData.paymentMethod || 
        !formData.txid || !formData.proofImage) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)
      
      const orderData = {
        email: formData.email,
        challenge_type: formData.challengeType,
        account_size: formData.size,
        platform: formData.platform,
        payment_method: formData.paymentMethod,
        txid: formData.txid,
        image: formData.proofImage!,
      }

      await createOrder(orderData)
      
      toast({
        title: "Order Placed Successfully!",
        description: "Your order has been submitted and is being processed. You will receive an email confirmation shortly.",
        duration: 5000,
      })
      
    setOrderPlaced(true)
    } catch (error) {
      console.error('Failed to place order:', error)
      toast({
        title: "Order Failed",
        description: error instanceof Error ? error.message : "Failed to place order. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedPaymentMethod = paymentMethods.find(pm => pm.value === formData.paymentMethod)
  const selectedChallengeType = challengeTypes.find(ct => ct.value === formData.challengeType)

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  }

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.5, ease: "easeOut" }
    },
    hover: {
      scale: 1.02,
      transition: { duration: 0.2 }
    }
  }

  // Calculate progress
  const totalSteps = 4
  const completedSteps = [
    formData.challengeType,
    formData.email && formData.platform && formData.size,
    formData.paymentMethod,
    formData.txid && formData.proofImage
  ].filter(Boolean).length
  const progress = (completedSteps / totalSteps) * 100

  if (orderPlaced) {
    return (
      <motion.div 
        className="flex items-center justify-center min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c]"
        variants={fadeInUp}
        initial="hidden"
        animate="visible"
      >
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full blur-3xl"></div>
          <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60 max-w-lg w-full shadow-2xl relative">
            <CardContent className="text-center p-12">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, type: "spring" }}
                className="relative mb-8"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full blur-xl"></div>
                <CheckCircle className="h-24 w-24 text-emerald-500 mx-auto relative z-10" />
              </motion.div>
              <motion.h2 
                className="text-4xl font-bold text-white mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                Order Placed Successfully!
              </motion.h2>
              <motion.p 
                className="text-gray-300 mb-8 leading-relaxed text-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
              Your order has been submitted and is being processed. You will receive an email confirmation shortly.
              </motion.p>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
            <Button 
              onClick={() => {
                setOrderPlaced(false)
                setFormData({
                  email: "",
                  challengeType: "",
                  platform: "",
                  size: "",
                  paymentMethod: "",
                  txid: "",
                  proofImage: null,
                })
                setSelectedPrice(0)
                setShowPayment(false)
              }}
                  className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 shadow-lg transform hover:scale-105"
            >
                  <Rocket className="mr-3 h-5 w-5" />
              Place Another Order
            </Button>
              </motion.div>
          </CardContent>
        </Card>
        </div>
      </motion.div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-r from-sky-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto p-6">
        {/* Header Section */}
        <motion.div 
          className="text-center mb-8"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, type: "spring" }}
            className="inline-block mb-6"
          >
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-sky-500/20 to-blue-500/20 rounded-full blur-xl"></div>
              <div className="relative bg-gradient-to-r from-sky-500 to-blue-500 p-4 rounded-full">
                <Crown className="h-12 w-12 text-white" />
              </div>
            </div>
          </motion.div>
          <motion.h1 
            className="text-5xl font-bold text-white mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            Professional Trading Account
          </motion.h1>
          <motion.p 
            className="text-xl text-gray-300 mb-6 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            Start your trading journey with our enterprise-grade accounts. 
            Choose your challenge and unlock your potential.
          </motion.p>
        </motion.div>

        {/* Progress Bar */}
        <motion.div 
          className="mb-8"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
        >
          <div className="flex items-center justify-between mb-4">
            <span className="text-white font-medium">Setup Progress</span>
            <span className="text-sky-400 font-bold">{completedSteps}/{totalSteps} Steps</span>
          </div>
          <div className="w-full bg-[#001a2c] rounded-full h-3">
            <motion.div 
              className="bg-gradient-to-r from-sky-500 to-blue-500 h-3 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
        </motion.div>

        {/* Main Form */}
        <motion.div 
          className="grid grid-cols-1 lg:grid-cols-3 gap-8"
          variants={staggerContainer}
          initial="hidden"
          animate="visible"
        >
          {/* Left Column - Challenge Types */}
          <div className="lg:col-span-2 space-y-8">
            {/* Challenge Type Selection */}
            <motion.div variants={fadeInUp} className="space-y-6">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full blur-lg"></div>
                  <div className="relative w-12 h-12 rounded-full bg-gradient-to-r from-sky-500 to-blue-500 flex items-center justify-center">
                    <span className="text-white font-bold text-lg">1</span>
                  </div>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">Select Challenge Type</h2>
                  <p className="text-gray-400">Choose your trading challenge</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {challengeTypes.map((type, index) => (
                  <motion.div
                    key={type.value}
                    variants={cardVariants}
                    whileHover="hover"
                    className={`relative group cursor-pointer ${
                      formData.challengeType === type.value
                        ? "ring-2 ring-sky-500 ring-offset-2 ring-offset-[#001a2c]"
                        : ""
                    }`}
                    onClick={() => handleInputChange("challengeType", type.value)}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-sky-500/10 to-blue-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                    <div className={`relative p-6 rounded-2xl border transition-all duration-300 ${
                      formData.challengeType === type.value
                        ? "bg-gradient-to-br from-sky-500/20 to-blue-500/20 border-sky-500 shadow-2xl"
                        : "bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 border-sky-900/60 hover:border-sky-500/50"
                    }`}>
                      {type.badge && (
                        <Badge className={`absolute -top-3 -right-3 bg-gradient-to-r ${type.color} text-white border-0 px-3 py-1`}>
                          {type.badge}
                        </Badge>
                      )}
                      {type.popular && (
                        <div className="absolute -top-3 -left-3">
                          <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                            MOST POPULAR
                          </div>
                        </div>
                      )}
                      
                      <div className="flex items-center mb-4">
                        <div className={`p-3 rounded-xl bg-gradient-to-r ${type.color} mr-3`}>
                          {type.icon}
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-white">{type.label}</h3>
                          <p className="text-gray-400 text-sm">{type.description}</p>
                        </div>
                      </div>
                      
                      <div className="mb-4">
                        <div className="text-2xl font-bold text-white mb-1">${type.price}</div>
                        <div className="text-gray-400 text-sm">One-time payment</div>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-2 mb-4">
                        <div className="text-center p-2 bg-[#001a2c] rounded-lg">
                          <div className="text-xs text-gray-400">Duration</div>
                          <div className="text-sm font-bold text-white">{type.duration}</div>
                        </div>
                        <div className="text-center p-2 bg-[#001a2c] rounded-lg">
                          <div className="text-xs text-gray-400">Profit Target</div>
                          <div className="text-sm font-bold text-green-400">{type.profitTarget}</div>
                        </div>
                        <div className="text-center p-2 bg-[#001a2c] rounded-lg">
                          <div className="text-xs text-gray-400">Max Drawdown</div>
                          <div className="text-sm font-bold text-red-400">{type.maxDrawdown}</div>
                        </div>
                      </div>
                      
                      <Button 
                        className={`w-full ${
                          formData.challengeType === type.value
                            ? "bg-gradient-to-r from-sky-500 to-blue-500 text-white"
                            : "bg-gradient-to-r from-gray-600 to-gray-700 text-gray-300 hover:from-sky-500 hover:to-blue-500 hover:text-white"
                        } transition-all duration-300`}
                      >
                        {formData.challengeType === type.value ? (
                          <>
                            <Check className="mr-2 h-4 w-4" />
                            Selected
                          </>
                        ) : (
                          <>
                            <ArrowRight className="mr-2 h-4 w-4" />
                            Select Plan
                          </>
                        )}
                      </Button>
                  </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Account Configuration */}
            <motion.div variants={fadeInUp} className="space-y-6">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full blur-lg"></div>
                  <div className="relative w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                    <span className="text-white font-bold text-lg">2</span>
                  </div>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">Account Configuration</h2>
                  <p className="text-gray-400">Configure your trading setup</p>
              </div>
            </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <Label className="text-white font-medium">Email Address</Label>
                  <div className="relative">
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                      className="bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 border-sky-900/60 text-white h-12 pl-12"
                      placeholder="Enter your professional email"
                />
                    <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  </div>
            </div>

                <div className="space-y-4">
                  <Label className="text-white font-medium">Trading Platform</Label>
                <Select value={formData.platform} onValueChange={(value) => handleInputChange("platform", value)}>
                    <SelectTrigger className="bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 border-sky-900/60 text-white h-12">
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                    <SelectContent className="bg-gradient-to-br from-[#001a2c] to-[#002a3c] border-sky-900/60">
                    {platforms.map((platform) => (
                        <SelectItem key={platform.value} value={platform.value} className="text-white hover:bg-sky-500/20">
                          <div className="flex items-center">
                            <span className="mr-3 text-xl">{platform.icon}</span>
                            <div>
                              <div className="font-medium">{platform.label}</div>
                              <div className="text-sm text-gray-400">{platform.description}</div>
              </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
              
              <div className="space-y-4">
                <Label className="text-white font-medium">Account Size</Label>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  {sizes.map((size, idx) => {
                    const challengeKey = formData.challengeType || 'instant';
                    const realPrice = priceTable[challengeKey]?.real[idx] || 0;
                    const discountPrice = priceTable[challengeKey]?.discount[idx] || 0;
                    return (
                      <div
                        key={size.value}
                        className={`relative p-4 rounded-xl border cursor-pointer transition-all duration-300 ${
                          formData.size === size.value
                            ? "bg-gradient-to-br from-sky-500/20 to-blue-500/20 border-sky-500"
                            : "bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 border-sky-900/60 hover:border-sky-500/50"
                        }`}
                        onClick={() => handleInputChange("size", size.value)}
                      >
                        <div className="text-center">
                          <div className="text-lg font-bold text-white mb-1">{size.label}</div>
                          <div className="flex flex-col items-center mb-2">
                            <span className="text-base text-gray-400 line-through">${realPrice}</span>
                            <span className="text-xl font-bold text-green-400">${discountPrice}</span>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            </motion.div>
          </div>

          {/* Right Column - Payment & Summary */}
          <div className="space-y-6">
            {/* Order Summary */}
            <motion.div variants={fadeInUp}>
              <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60 shadow-2xl">
                <CardHeader className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-b border-sky-900/60 p-6">
                  <CardTitle className="text-white flex items-center gap-3">
                    <Trophy className="h-6 w-6 text-purple-400" />
                    Order Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 space-y-4">
                  {selectedChallengeType && (
                    <div className="flex items-center justify-between p-4 bg-gradient-to-br from-[#001a2c] to-[#002a3c] rounded-xl border border-sky-900/60">
                      <div>
                        <div className="text-white font-medium">{selectedChallengeType.label}</div>
                        <div className="text-gray-400 text-sm">{selectedChallengeType.description}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-white">${selectedPrice}</div>
                        <div className="text-sky-400 text-sm">Setup Fee</div>
                      </div>
                    </div>
                  )}
                  
                  <div className="border-t border-sky-900/60 pt-4">
                    <div className="flex items-center justify-between">
                      <div className="text-xl font-bold text-white">Total</div>
                      <div className="text-3xl font-bold bg-gradient-to-r from-sky-400 to-blue-500 text-transparent bg-clip-text">
                        ${(selectedChallengeType?.price || 0) + selectedPrice}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Payment Method */}
            <motion.div variants={fadeInUp}>
              <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60 shadow-2xl">
                <CardHeader className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-b border-sky-900/60 p-6">
                  <CardTitle className="text-white flex items-center gap-3">
                    <CreditCard className="h-6 w-6 text-green-400" />
                    Payment Method
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 space-y-4">
              <Select value={formData.paymentMethod} onValueChange={(value) => handleInputChange("paymentMethod", value)}>
                    <SelectTrigger className="bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 border-sky-900/60 text-white h-12">
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                    <SelectContent className="bg-gradient-to-br from-[#001a2c] to-[#002a3c] border-sky-900/60">
                  {paymentMethods.map((method) => (
                        <SelectItem key={method.value} value={method.value} className="text-white hover:bg-sky-500/20">
                          <div className="flex items-center justify-between w-full">
                            <div>
                              <div className="font-medium">{method.label}</div>
                              <div className="text-sm text-gray-400">{method.network}</div>
                            </div>
                            <div className="text-right">
                              <Badge className="bg-green-500/20 text-green-400 border border-green-500/30">
                                {method.fee} Fee
                              </Badge>
                              <div className="text-xs text-gray-400 mt-1">{method.speed}</div>
                            </div>
                          </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
                </CardContent>
              </Card>
            </motion.div>

            {/* Payment Details */}
            <AnimatePresence>
            {showPayment && selectedPaymentMethod && (
              <motion.div 
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
                  exit="hidden"
                >
                  <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60 shadow-2xl">
                    <CardHeader className="bg-gradient-to-r from-orange-500/20 to-red-500/20 border-b border-sky-900/60 p-6">
                      <CardTitle className="text-white flex items-center gap-3">
                        <QrCode className="h-6 w-6 text-orange-400" />
                        Payment Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-6 space-y-6">
                      <div className="text-center">
                        <div className="bg-white p-4 rounded-xl shadow-lg inline-block">
                          <img 
                            src={selectedPaymentMethod.qr} 
                            alt="QR Code"
                            className="w-48 h-48"
                          />
                        </div>
                        <div className="mt-4 p-4 bg-gradient-to-br from-[#001a2c] to-[#002a3c] rounded-xl border border-sky-900/60">
                          <div className="text-3xl font-bold bg-gradient-to-r from-sky-400 to-blue-500 text-transparent bg-clip-text">
                            ${(selectedChallengeType?.price || 0) + selectedPrice}
                          </div>
                          <div className="text-gray-400 text-sm">Amount to Pay</div>
                    </div>
                  </div>
                  
                      <div className="space-y-4">
                        <div>
                          <Label className="text-white font-medium">Transaction ID (TXID)</Label>
                      <Input
                        value={formData.txid}
                        onChange={(e) => handleInputChange("txid", e.target.value)}
                            className="bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 border-sky-900/60 text-white h-12"
                        placeholder="Enter transaction ID"
                      />
                    </div>
                    
                        <div>
                          <Label className="text-white font-medium">Payment Proof</Label>
                      <div className="flex items-center space-x-3">
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handleFileUpload}
                              className="bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 border-sky-900/60 text-white file:bg-gradient-to-r file:from-sky-500 file:to-blue-500 file:text-white file:border-0 file:rounded-lg file:px-4 file:py-2 file:mr-4 file:hover:from-sky-600 file:hover:to-blue-600 file:transition-all"
                        />
                            <Upload className="h-5 w-5 text-sky-400" />
                      </div>
                      {formData.proofImage && (
                        <p className="text-sm text-sky-400 mt-2 flex items-center">
                          <CheckCircle className="h-4 w-4 mr-2" />
                          {formData.proofImage.name}
                        </p>
                      )}
                    </div>
                  </div>
                    </CardContent>
                  </Card>
              </motion.div>
            )}
            </AnimatePresence>

            {/* Submit Button */}
            <motion.div variants={fadeInUp}>
            <Button 
              onClick={handlePlaceOrder}
              disabled={!formData.email || !formData.challengeType || 
                       !formData.platform || !formData.size || !formData.paymentMethod || 
                       !formData.txid || !formData.proofImage || isSubmitting}
                className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white text-xl font-bold py-6 rounded-2xl shadow-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
            >
              {isSubmitting ? (
                <>
                    <Loader2 className="mr-3 h-6 w-6 animate-spin" />
                  Processing Order...
                </>
              ) : (
                  <>
                    <Rocket className="mr-3 h-6 w-6" />
                    {`Place Order - $${(selectedChallengeType?.price || 0) + selectedPrice}`}
                  </>
              )}
            </Button>
            </motion.div>
          </div>
      </motion.div>
      </div>
    </div>
  )
}
